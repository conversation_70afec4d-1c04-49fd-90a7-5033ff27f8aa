
from pprint import pprint
from time import time
from datetime import datetime
from pyqlab.data.dataset import DataHandler
from pyqlab.const import (
    TOP_FUT_CODES, MAIN_FUT_CODES, MAIN_SEL_FUT_CODES, SF_FUT_CODES,
    SEL_FACTOR_NAMES,
)
from pyqlab.utils import init_instance_by_config
from pyqlab.data.dataset.handler import LabelThresholds, DirectionType


FUT_CODES=MAIN_SEL_FUT_CODES

LabelThresholds = LabelThresholds(
    long_threshold=0.3, short_threshold=-0.3)

data_handler_config = {
    "start_date": "",
    "end_date": "",
    "years": ['2025',],
    "win": 0,                       # 采样窗口,与下面的num_channel通道数保持一致
    "step": 1,                      # 采样步长，通常为1
    "filter_win": 0,                # 是否过滤掉特征数据
    "is_filter_extreme": False,     # 是否过滤极端值
    "is_normal": True,              # 是否归一化
    "verbose": False,               # 是否打印日志
    "sel_fd_names": SEL_FACTOR_NAMES,
    "main_fd_name": "fd_1_0",
    "direct": DirectionType.LONG_SHORT.value,
    "label_thresholds": {
    "long_threshold": 0.3, "short_threshold": -0.3,
        "multi_label_positive": 0.3, "multi_label_negative": -0.3
    },
    "data_loader": {
        "data_path": "f:/featdata/top",
        "train_codes": TOP_FUT_CODES,   # 选期货交易标的
        "years": ['2025',],
        "suffix": "top",
        "fd_set": {(1,0), (1,1)}
    },
}

handler_class_config = {
            "class": "DataHandler",
            "module_path": "pyqlab.data",
            "kwargs": data_handler_config,
        }

dataset_config = {
    "class": "FTSDataset",
    "module_path": "pyqlab.data",
    "kwargs": {
        "handler": handler_class_config,
        "model_type": 0,
        "seq_len": 20,
        "pred_len": 1,
    },
}

def get_dataset(ds_files=[],
                is_normal=True,
                verbose=False,
                start_date="",
                end_date="",
                model_type=0,
                seq_len=32,
                label_len=0,
                pred_len=1,
                ):
    data_handler_config["years"] = ds_files
    data_handler_config["start_date"] = start_date
    data_handler_config["end_date"] = end_date
    data_handler_config["win"] = seq_len
    data_handler_config["is_normal"] = is_normal
    data_handler_config["verbose"] = verbose

    handler_class_config["kwargs"] = data_handler_config
    hd: DataHandler = init_instance_by_config(handler_class_config)
    dataset_config["kwargs"]["handler"] = hd
    dataset_config["kwargs"]["model_type"] = model_type
    dataset_config["kwargs"]["seq_len"] = seq_len
    dataset_config["kwargs"]["label_len"] = label_len
    dataset_config["kwargs"]["pred_len"] = pred_len
    dataset = init_instance_by_config(dataset_config)
    dataset.setup_data(handler_kwargs=data_handler_config)
    return dataset


if __name__ == "__main__":
    dataset = get_dataset(ds_files=["2025"])
    dataset.load_data()
    print(len(dataset))
    print(next(iter(dataset)))
